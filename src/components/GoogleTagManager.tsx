import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import TagManager from 'react-gtm-module';

interface GoogleTagManagerProps {
  gtmId: string;
  enabled?: boolean;
}

const GoogleTagManager: React.FC<GoogleTagManagerProps> = ({
  gtmId,
  enabled = false,
}) => {
  const location = useLocation();

  // 初始化 GTM
  useEffect(() => {
    if (!enabled) return;

    TagManager.initialize({ gtmId });
  }, [gtmId, enabled]);

  // 路由变化时发送页面浏览事件
  useEffect(() => {
    if (!enabled) return;

    TagManager.dataLayer({
      dataLayer: {
        event: 'page_view',
        page_path: location.pathname,
      },
    });
  }, [location, enabled]);

  return null;
};

export default GoogleTagManager;
