import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import TagManager from 'react-gtm-module';

interface GoogleTagManagerProps {
  gtmId: string;
  enabled?: boolean;
}

const GoogleTagManager: React.FC<GoogleTagManagerProps> = ({ 
  gtmId, 
  enabled = false 
}) => {
  const location = useLocation();

  // 初始化 GTM
  useEffect(() => {
    if (!enabled) {
      console.log('GTM 已禁用 - 当前环境不加载 Google Tag Manager');
      return;
    }

    // 初始化 GTM
    TagManager.initialize({
      gtmId: gtmId,
      dataLayer: {
        // 可以添加一些初始数据
        environment: import.meta.env.MODE,
        version: '1.0.0',
      },
    });

    console.log(`GTM 已初始化 - ID: ${gtmId}, 环境: ${import.meta.env.MODE}`);
  }, [gtmId, enabled]);

  // 监听路由变化，发送页面浏览事件
  useEffect(() => {
    if (!enabled) return;

    // 发送页面浏览事件
    TagManager.dataLayer({
      dataLayer: {
        event: 'page_view',
        page_title: document.title,
        page_location: window.location.href,
        page_path: location.pathname + location.search,
      },
    });

    console.log('GTM 页面浏览事件已发送:', location.pathname);
  }, [location, enabled]);

  return null; // 这个组件不渲染任何内容
};

export default GoogleTagManager;
