import TagManager from 'react-gtm-module';

/**
 * GTM 工具函数
 * 用于手动发送各种事件到 Google Tag Manager
 */

// 检查 GTM 是否启用
const isGTMEnabled = () => {
  return import.meta.env.VITE_ENABLE_GTM === 'true';
};

/**
 * 发送自定义事件
 * @param eventName 事件名称
 * @param parameters 事件参数
 */
export const sendGTMEvent = (eventName: string, parameters: Record<string, any> = {}) => {
  if (!isGTMEnabled()) {
    console.log('GTM 已禁用，跳过事件发送:', eventName);
    return;
  }

  TagManager.dataLayer({
    dataLayer: {
      event: eventName,
      ...parameters,
    },
  });

  console.log('GTM 事件已发送:', eventName, parameters);
};

/**
 * 发送页面浏览事件（通常由组件自动处理，这里提供手动调用的选项）
 * @param pagePath 页面路径
 * @param pageTitle 页面标题
 */
export const sendPageView = (pagePath: string, pageTitle?: string) => {
  sendGTMEvent('page_view', {
    page_title: pageTitle || document.title,
    page_location: window.location.href,
    page_path: pagePath,
  });
};

/**
 * 发送点击事件
 * @param elementName 元素名称
 * @param elementCategory 元素分类
 * @param value 值（可选）
 */
export const sendClickEvent = (elementName: string, elementCategory: string, value?: number) => {
  sendGTMEvent('click', {
    event_category: elementCategory,
    event_label: elementName,
    value: value,
  });
};

/**
 * 发送用户登录事件
 * @param method 登录方式
 * @param userId 用户ID（可选）
 */
export const sendLoginEvent = (method: string, userId?: string) => {
  sendGTMEvent('login', {
    method: method,
    user_id: userId,
  });
};

/**
 * 发送用户注册事件
 * @param method 注册方式
 * @param userId 用户ID（可选）
 */
export const sendSignUpEvent = (method: string, userId?: string) => {
  sendGTMEvent('sign_up', {
    method: method,
    user_id: userId,
  });
};

/**
 * 发送搜索事件
 * @param searchTerm 搜索词
 * @param category 搜索分类（可选）
 */
export const sendSearchEvent = (searchTerm: string, category?: string) => {
  sendGTMEvent('search', {
    search_term: searchTerm,
    search_category: category,
  });
};

/**
 * 发送购买事件
 * @param transactionId 交易ID
 * @param value 交易金额
 * @param currency 货币类型
 * @param items 商品列表（可选）
 */
export const sendPurchaseEvent = (
  transactionId: string, 
  value: number, 
  currency: string = 'CNY',
  items?: Array<{
    item_id: string;
    item_name: string;
    category: string;
    quantity: number;
    price: number;
  }>
) => {
  sendGTMEvent('purchase', {
    transaction_id: transactionId,
    value: value,
    currency: currency,
    items: items,
  });
};

/**
 * 设置用户属性
 * @param userId 用户ID
 * @param userProperties 用户属性
 */
export const setUserProperties = (userId: string, userProperties: Record<string, any> = {}) => {
  if (!isGTMEnabled()) {
    console.log('GTM 已禁用，跳过用户属性设置');
    return;
  }

  TagManager.dataLayer({
    dataLayer: {
      user_id: userId,
      ...userProperties,
    },
  });

  console.log('GTM 用户属性已设置:', userId, userProperties);
};
